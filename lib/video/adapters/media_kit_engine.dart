import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart' as mk;
import 'package:media_kit_video/media_kit_video.dart' as mkv;
import '../../services/logger_service.dart';
import '../../utils/h265_compatibility_detector.dart';
import '../engine.dart';
import '../policy.dart';

class MediaKitEngine implements VideoEngine {
  final VideoPolicy policy;
  mk.Player? _player;
  mkv.VideoController? _videoController;
  H265CompatibilityInfo? _compatibilityInfo;
  bool _usingSoftwareDecoding = false;
  bool _failedWithHardware = false;

  MediaKitEngine(this.policy);

  @override
  Future<void> init() async {
    // 获取设备兼容性信息
    _compatibilityInfo = await H265CompatibilityDetector.getCompatibilityInfo();
    logger.i('MediaKitEngine: 设备兼容性信息: $_compatibilityInfo');

    // 根据策略和设备兼容性确定解码方式
    final hwDecPolicy = _determineHwDecPolicy();
    logger.i('MediaKitEngine: 使用解码策略: $hwDecPolicy');

    // 创建播放器配置
    final playerConfig = _createPlayerConfiguration(hwDecPolicy);

    // 初始化播放器
    _player = mk.Player(configuration: playerConfig);

    // 循环播放单个媒体：设置播放列表模式为循环
    try {
      await _player!.setPlaylistMode(mk.PlaylistMode.loop);
    } catch (e) {
      logger.w('MediaKitEngine: 设置循环播放失败: $e');
    }

    // 创建视频控制器配置
    final videoConfig = _createVideoControllerConfiguration(hwDecPolicy);
    _videoController = mkv.VideoController(
      _player!,
      configuration: videoConfig,
    );
  }

  /// 根据策略和设备兼容性确定硬件解码策略
  HwDecPolicy _determineHwDecPolicy() {
    // 如果策略明确指定了解码方式，则使用指定的方式
    if (policy.hwDec != HwDecPolicy.auto) {
      return policy.hwDec;
    }

    // 检查视频格式和编码
    final videoInfo = _analyzeVideoFile(policy.sourcePath);
    logger.i('MediaKitEngine: 视频分析结果: $videoInfo');

    // 获取设备兼容性信息
    final info = _compatibilityInfo ?? H265CompatibilityInfo.createFallback();

    // 根据视频分析结果决定解码策略
    final recommendedDecoding = videoInfo['recommendedDecoding'] as String;

    if (recommendedDecoding == 'forceSoftware') {
      _usingSoftwareDecoding = true;
      logger.i('MediaKitEngine: 根据视频分析，强制使用软件解码');
      return HwDecPolicy.forceSoftware;
    }

    // 对于问题视频，直接使用软解码
    if (videoInfo['isProblemVideo'] == true) {
      _usingSoftwareDecoding = true;
      logger.i('MediaKitEngine: 检测到问题视频，强制使用软件解码');
      return HwDecPolicy.forceSoftware;
    }

    // 优先尝试硬解码策略：除非明确知道不可靠，否则先尝试硬解
    if (info.hasHardwareDecoder) {
      // 对于已知问题设备（如RK3568）的H.265视频，直接使用软解避免花屏
      if (info.isRK3568 && videoInfo['isH265'] == true) {
        _usingSoftwareDecoding = true;
        logger.i('MediaKitEngine: RK3568设备H.265视频使用软件解码（避免花屏问题）');
        return HwDecPolicy.forceSoftware;
      }

      // 对于特殊格式或高分辨率视频，考虑使用软解码
      if (videoInfo['isSpecialFormat'] == true ||
          videoInfo['isHighRes'] == true) {
        logger.i('MediaKitEngine: 检测到特殊格式或高分辨率视频，优先使用软解码');
        _usingSoftwareDecoding = true;
        return HwDecPolicy.forceSoftware;
      }

      // 其他情况优先尝试硬解码
      logger.i('MediaKitEngine: 优先尝试硬件解码');
      return HwDecPolicy.forceHardware;
    } else {
      _usingSoftwareDecoding = true;
      logger.i('MediaKitEngine: 设备无硬件解码器，使用软件解码');
      return HwDecPolicy.forceSoftware;
    }
  }

  /// 分析视频文件格式和特征
  Map<String, dynamic> _analyzeVideoFile(String sourcePath) {
    final result = <String, dynamic>{};
    final pathLower = sourcePath.toLowerCase();
    final fileName = sourcePath.split('/').last.toLowerCase();

    // 检查是否为H.265/HEVC视频
    result['isH265'] =
        policy.codecHint.toLowerCase().contains('h265') ||
        pathLower.contains('h265') ||
        pathLower.contains('hevc');

    // 检查是否为特殊格式
    result['isSpecialFormat'] =
        pathLower.contains('mjpeg') ||
        pathLower.contains('vc1') ||
        pathLower.contains('vp9') ||
        fileName.contains('特殊') ||
        fileName.contains('special');

    // 检查是否为高分辨率视频（通过文件名推测）
    result['isHighRes'] =
        pathLower.contains('4k') ||
        pathLower.contains('2160p') ||
        pathLower.contains('1440p') ||
        pathLower.contains('uhd');

    // 检查文件扩展名
    final extension = pathLower.split('.').last;
    result['extension'] = extension;
    result['isCommonFormat'] = [
      'mp4',
      'avi',
      'mkv',
      'mov',
      'wmv',
    ].contains(extension);

    // 检查是否为问题视频（基于文件名模式）
    result['isProblemVideo'] = _isProblemVideo(fileName, pathLower);

    // 推荐解码策略
    result['recommendedDecoding'] = _getRecommendedDecoding(result);

    return result;
  }

  /// 检查是否为已知的问题视频
  bool _isProblemVideo(String fileName, String pathLower) {
    // 检查文件名中是否包含已知的问题标识
    final problemPatterns = [
      '广州', // 从日志中看到的问题视频
      'problem',
      'issue',
      'broken',
      'corrupt',
    ];

    for (final pattern in problemPatterns) {
      if (fileName.contains(pattern) || pathLower.contains(pattern)) {
        return true;
      }
    }

    return false;
  }

  /// 获取推荐的解码方式
  String _getRecommendedDecoding(Map<String, dynamic> videoInfo) {
    // 如果是问题视频，直接推荐软解码
    if (videoInfo['isProblemVideo'] == true) {
      return 'forceSoftware';
    }

    // 如果是特殊格式，推荐软解码
    if (videoInfo['isSpecialFormat'] == true) {
      return 'forceSoftware';
    }

    // 如果是H.265且设备是RK3568，推荐软解码
    if (videoInfo['isH265'] == true && _compatibilityInfo?.isRK3568 == true) {
      return 'forceSoftware';
    }

    // 其他情况尝试硬解码
    return 'auto';
  }

  /// 创建播放器配置
  mk.PlayerConfiguration _createPlayerConfiguration(HwDecPolicy hwDecPolicy) {
    return mk.PlayerConfiguration(
      ready: () {
        logger.i('MediaKitEngine: 播放器初始化完成');
      },
      title: '视频播放器',
    );
  }

  /// 创建视频控制器配置
  mkv.VideoControllerConfiguration _createVideoControllerConfiguration(
    HwDecPolicy hwDecPolicy,
  ) {
    logger.i('MediaKitEngine: 创建视频控制器配置，解码策略: $hwDecPolicy');

    // 根据解码策略设置硬件解码选项
    String hwdecValue;
    bool enableHardwareAcceleration;

    switch (hwDecPolicy) {
      case HwDecPolicy.forceHardware:
        hwdecValue = 'auto-safe';
        enableHardwareAcceleration = true;
        logger.i('MediaKitEngine: 强制使用硬件解码');
        break;
      case HwDecPolicy.forceSoftware:
        hwdecValue = 'no';
        enableHardwareAcceleration = false;
        logger.i('MediaKitEngine: 强制使用软件解码');
        break;
      case HwDecPolicy.disabled:
        hwdecValue = 'no';
        enableHardwareAcceleration = false;
        logger.i('MediaKitEngine: 禁用硬件解码');
        break;
      case HwDecPolicy.auto:
        hwdecValue = 'auto';
        enableHardwareAcceleration = true;
        logger.i('MediaKitEngine: 自动选择解码方式');
        break;
    }

    // 如果是低性能设备，使用更保守的设置
    if (_compatibilityInfo != null &&
        _compatibilityInfo!.softwareDecoderPerformance ==
            SoftwareDecoderPerformance.low) {
      hwdecValue = 'no';
      enableHardwareAcceleration = false;
      logger.i('MediaKitEngine: 低性能设备，强制使用软件解码');
    }

    return mkv.VideoControllerConfiguration(
      hwdec: hwdecValue,
      vo: 'gpu',
      enableHardwareAcceleration: enableHardwareAcceleration,
    );
  }

  @override
  Future<void> open(String sourcePath) async {
    assert(_player != null);
    final isHttp =
        sourcePath.startsWith('http://') || sourcePath.startsWith('https://');
    final isFileUri =
        sourcePath.startsWith('file://') || sourcePath.startsWith('content://');
    final uri = (isHttp || isFileUri)
        ? sourcePath
        : Uri.file(sourcePath).toString();

    logger.i('MediaKitEngine: 准备打开视频: $uri');
    logger.i(
      'MediaKitEngine: 当前解码模式: ${_usingSoftwareDecoding ? "软解码" : "硬解码"}',
    );

    try {
      // 监听播放错误（在打开之前设置）
      _player!.stream.error.listen((error) {
        logger.e('MediaKitEngine: 播放错误: $error');
        _handlePlaybackError(uri, error);
      });

      // 尝试打开视频
      logger.i('MediaKitEngine: 开始打开媒体文件');
      await _player!.open(mk.Media(uri));

      logger.i('MediaKitEngine: 成功打开视频: $uri');

      // 如果之前失败过，记录成功信息
      if (_failedWithHardware) {
        logger.i('MediaKitEngine: 使用软解码成功播放之前失败的视频');
      }
    } catch (e) {
      logger.e('MediaKitEngine: 打开视频失败: $e');
      logger.e('MediaKitEngine: 错误类型: ${e.runtimeType}');

      // 检查是否是编解码器相关错误
      final errorString = e.toString().toLowerCase();
      final isCodecError =
          errorString.contains('codec') ||
          errorString.contains('decoder') ||
          errorString.contains('format') ||
          errorString.contains('unsupported');

      logger.i('MediaKitEngine: 是否为编解码器错误: $isCodecError');

      // 如果使用硬解失败，尝试切换到软解
      if (!_usingSoftwareDecoding && isCodecError) {
        logger.i('MediaKitEngine: 检测到编解码器错误，尝试切换到软解码');
        await _handlePlaybackError(uri, e.toString());
      } else {
        // 如果已经是软解还失败，或者不是编解码器错误，则抛出异常
        logger.e('MediaKitEngine: 无法通过切换解码方式解决，抛出异常');
        rethrow;
      }
    }
  }

  /// 处理播放错误，尝试多种回退策略
  Future<void> _handlePlaybackError(String uri, dynamic error) async {
    // 如果已经在使用软解码或已经尝试过切换，尝试其他策略
    if (_usingSoftwareDecoding || _failedWithHardware) {
      logger.w('MediaKitEngine: 已经在使用软解码，尝试其他恢复策略');
      await _tryAlternativeStrategies(uri, error);
      return;
    }

    logger.i('MediaKitEngine: 硬解播放失败，尝试切换到软解码');
    logger.i('MediaKitEngine: 错误详情: $error');

    _failedWithHardware = true;
    _usingSoftwareDecoding = true;

    try {
      await _recreatePlayerWithSoftwareDecoding(uri);
      logger.i('MediaKitEngine: 切换到软解码后成功打开视频');
    } catch (e) {
      logger.e('MediaKitEngine: 切换到软解码后仍然失败: $e');
      // 尝试其他策略
      await _tryAlternativeStrategies(uri, e);
    }
  }

  /// 重新创建播放器并使用软解码
  Future<void> _recreatePlayerWithSoftwareDecoding(String uri) async {
    // 停止当前播放
    logger.i('MediaKitEngine: 停止当前播放器');
    await _player?.stop();

    // 重新创建播放器配置，强制使用软解码
    logger.i('MediaKitEngine: 创建软解码播放器配置');
    final playerConfig = _createPlayerConfiguration(HwDecPolicy.forceSoftware);

    // 重新初始化播放器
    logger.i('MediaKitEngine: 重新初始化播放器');
    await _player?.dispose();
    _player = mk.Player(configuration: playerConfig);

    // 设置错误监听器
    _player!.stream.error.listen((error) {
      logger.e('MediaKitEngine: 软解播放错误: $error');
    });

    await _player!.setPlaylistMode(mk.PlaylistMode.loop);

    // 重新创建视频控制器配置
    final videoConfig = _createVideoControllerConfiguration(
      HwDecPolicy.forceSoftware,
    );
    _videoController = mkv.VideoController(
      _player!,
      configuration: videoConfig,
    );

    // 重新打开视频
    logger.i('MediaKitEngine: 使用软解码重新打开视频');
    await _player!.open(mk.Media(uri));
  }

  /// 尝试其他恢复策略
  Future<void> _tryAlternativeStrategies(String uri, dynamic error) async {
    logger.i('MediaKitEngine: 尝试其他恢复策略');

    // 策略1: 使用最保守的配置
    try {
      logger.i('MediaKitEngine: 尝试策略1 - 最保守配置');
      await _recreatePlayerWithConservativeSettings(uri);
      logger.i('MediaKitEngine: 策略1成功');
      return;
    } catch (e) {
      logger.e('MediaKitEngine: 策略1失败: $e');
    }

    // 策略2: 延迟重试
    try {
      logger.i('MediaKitEngine: 尝试策略2 - 延迟重试');
      await Future.delayed(const Duration(seconds: 2));
      await _recreatePlayerWithSoftwareDecoding(uri);
      logger.i('MediaKitEngine: 策略2成功');
      return;
    } catch (e) {
      logger.e('MediaKitEngine: 策略2失败: $e');
    }

    // 所有策略都失败
    logger.e('MediaKitEngine: 所有恢复策略都失败');
    throw Exception('视频播放失败，所有恢复策略都无法播放: $error');
  }

  /// 使用最保守的设置重新创建播放器
  Future<void> _recreatePlayerWithConservativeSettings(String uri) async {
    logger.i('MediaKitEngine: 使用最保守设置重新创建播放器');

    await _player?.stop();
    await _player?.dispose();

    // 最保守的播放器配置
    _player = mk.Player(
      configuration: mk.PlayerConfiguration(
        ready: () => logger.i('MediaKitEngine: 保守配置播放器初始化完成'),
        title: '视频播放器（保守模式）',
      ),
    );

    await _player!.setPlaylistMode(mk.PlaylistMode.loop);

    // 最保守的视频控制器配置
    _videoController = mkv.VideoController(
      _player!,
      configuration: const mkv.VideoControllerConfiguration(
        hwdec: 'no',
        vo: 'gpu',
        enableHardwareAcceleration: false,
      ),
    );

    await _player!.open(mk.Media(uri));
  }

  @override
  Future<void> pause() async {
    await _player?.pause();
  }

  @override
  Future<void> play() async {
    await _player?.play();
  }

  @override
  Future<void> stop() async {
    await _player?.stop();
  }

  @override
  Future<void> dispose() async {
    try {
      await _player?.dispose();
    } catch (e) {
      logger.w('MediaKitEngine: player dispose error: $e');
    }
    _videoController = null; // Video widget controller will be GC'd
  }

  @override
  Widget buildView() {
    if (_videoController == null) {
      return const SizedBox();
    }

    // 如果是软解码，添加一些优化设置
    if (_usingSoftwareDecoding) {
      return mkv.Video(
        controller: _videoController!,
        // 软解码时使用cover模式，铺满屏幕且内容居中
        fit: BoxFit.cover,
        filterQuality: FilterQuality.low,
      );
    }

    // 使用cover模式，自动铺满屏幕且内容居中
    return mkv.Video(controller: _videoController!, fit: BoxFit.cover);
  }
}

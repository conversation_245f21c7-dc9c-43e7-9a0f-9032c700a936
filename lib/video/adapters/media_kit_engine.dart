import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart' as mk;
import 'package:media_kit_video/media_kit_video.dart' as mkv;
import '../../services/logger_service.dart';
import '../../utils/h265_compatibility_detector.dart';
import '../engine.dart';
import '../policy.dart';

class MediaKitEngine implements VideoEngine {
  final VideoPolicy policy;
  mk.Player? _player;
  mkv.VideoController? _videoController;
  H265CompatibilityInfo? _compatibilityInfo;
  bool _usingSoftwareDecoding = false;
  bool _failedWithHardware = false;

  MediaKitEngine(this.policy);

  @override
  Future<void> init() async {
    // 获取设备兼容性信息
    _compatibilityInfo = await H265CompatibilityDetector.getCompatibilityInfo();
    logger.i('MediaKitEngine: 设备兼容性信息: $_compatibilityInfo');

    // 根据策略和设备兼容性确定解码方式
    final hwDecPolicy = _determineHwDecPolicy();
    logger.i('MediaKitEngine: 使用解码策略: $hwDecPolicy');

    // 创建播放器配置
    final playerConfig = _createPlayerConfiguration(hwDecPolicy);

    // 初始化播放器
    _player = mk.Player(configuration: playerConfig);

    // 循环播放单个媒体：设置播放列表模式为循环
    try {
      await _player!.setPlaylistMode(mk.PlaylistMode.loop);
    } catch (e) {
      logger.w('MediaKitEngine: 设置循环播放失败: $e');
    }

    _videoController = mkv.VideoController(_player!);
  }

  /// 根据策略和设备兼容性确定硬件解码策略
  HwDecPolicy _determineHwDecPolicy() {
    // 如果策略明确指定了解码方式，则使用指定的方式
    if (policy.hwDec != HwDecPolicy.auto) {
      return policy.hwDec;
    }

    // 检查视频格式和编码
    final videoInfo = _analyzeVideoFile(policy.sourcePath);
    logger.i('MediaKitEngine: 视频分析结果: $videoInfo');

    // 获取设备兼容性信息
    final info = _compatibilityInfo ?? H265CompatibilityInfo.createFallback();

    // 优先尝试硬解码策略：除非明确知道不可靠，否则先尝试硬解
    if (info.hasHardwareDecoder) {
      // 对于已知问题设备（如RK3568）的H.265视频，直接使用软解避免花屏
      if (info.isRK3568 && videoInfo['isH265'] == true) {
        _usingSoftwareDecoding = true;
        logger.i('MediaKitEngine: RK3568设备H.265视频使用软件解码（避免花屏问题）');
        return HwDecPolicy.forceSoftware;
      }

      // 对于特殊格式或高分辨率视频，考虑使用软解码
      if (videoInfo['isSpecialFormat'] == true ||
          videoInfo['isHighRes'] == true) {
        logger.i('MediaKitEngine: 检测到特殊格式或高分辨率视频，优先使用软解码');
        _usingSoftwareDecoding = true;
        return HwDecPolicy.forceSoftware;
      }

      // 其他情况优先尝试硬解码
      logger.i('MediaKitEngine: 优先尝试硬件解码');
      return HwDecPolicy.forceHardware;
    } else {
      _usingSoftwareDecoding = true;
      logger.i('MediaKitEngine: 设备无硬件解码器，使用软件解码');
      return HwDecPolicy.forceSoftware;
    }
  }

  /// 分析视频文件格式和特征
  Map<String, dynamic> _analyzeVideoFile(String sourcePath) {
    final result = <String, dynamic>{};
    final pathLower = sourcePath.toLowerCase();
    final fileName = sourcePath.split('/').last.toLowerCase();

    // 检查是否为H.265/HEVC视频
    result['isH265'] =
        policy.codecHint.toLowerCase().contains('h265') ||
        pathLower.contains('h265') ||
        pathLower.contains('hevc');

    // 检查是否为特殊格式
    result['isSpecialFormat'] =
        pathLower.contains('mjpeg') ||
        pathLower.contains('vc1') ||
        pathLower.contains('vp9') ||
        fileName.contains('特殊') ||
        fileName.contains('special');

    // 检查是否为高分辨率视频（通过文件名推测）
    result['isHighRes'] =
        pathLower.contains('4k') ||
        pathLower.contains('2160p') ||
        pathLower.contains('1440p') ||
        pathLower.contains('uhd');

    // 检查文件扩展名
    final extension = pathLower.split('.').last;
    result['extension'] = extension;
    result['isCommonFormat'] = [
      'mp4',
      'avi',
      'mkv',
      'mov',
      'wmv',
    ].contains(extension);

    return result;
  }

  /// 创建播放器配置
  mk.PlayerConfiguration _createPlayerConfiguration(HwDecPolicy hwDecPolicy) {
    // 基础配置
    final Map<String, String> mpvOptions = {
      // 通用优化
      'vo': 'gpu',
      'gpu-api': 'auto',
      'hwdec-codecs': 'all',
      'video-sync': 'display-resample',
      'cache': 'yes',
      'cache-secs': '30',
    };

    // 根据解码策略设置硬件解码选项
    switch (hwDecPolicy) {
      case HwDecPolicy.forceHardware:
        mpvOptions['hwdec'] = 'auto-safe';
        break;
      case HwDecPolicy.forceSoftware:
        mpvOptions['hwdec'] = 'no';
        // 软解码优化
        mpvOptions['vd-lavc-threads'] = '4';
        mpvOptions['vd-lavc-skiploopfilter'] = 'nonkey';
        break;
      case HwDecPolicy.disabled:
        mpvOptions['hwdec'] = 'no';
        break;
      case HwDecPolicy.auto:
        mpvOptions['hwdec'] = 'auto';
        break;
    }

    // 如果是低性能设备，添加额外的优化
    if (_compatibilityInfo != null &&
        _compatibilityInfo!.softwareDecoderPerformance ==
            SoftwareDecoderPerformance.low) {
      mpvOptions['scale'] = 'bilinear';
      mpvOptions['dscale'] = 'bilinear';
      mpvOptions['sws-scaler'] = 'bilinear';
      mpvOptions['vd-lavc-fast'] = 'yes';
      mpvOptions['vd-lavc-skiploopfilter'] = 'all';
      mpvOptions['vd-lavc-skipframe'] = 'nonkey';
      mpvOptions['vd-lavc-framedrop'] = 'nonkey';
    }

    // 使用正确的参数名称 - media_kit 1.2.0版本
    // 由于无法直接设置MPV选项，我们使用基本配置
    return mk.PlayerConfiguration(
      ready: () {
        logger.i('MediaKitEngine: 播放器初始化完成');
      },
      title: '视频播放器',
    );
  }

  @override
  Future<void> open(String sourcePath) async {
    assert(_player != null);
    final isHttp =
        sourcePath.startsWith('http://') || sourcePath.startsWith('https://');
    final isFileUri =
        sourcePath.startsWith('file://') || sourcePath.startsWith('content://');
    final uri = (isHttp || isFileUri)
        ? sourcePath
        : Uri.file(sourcePath).toString();

    logger.i('MediaKitEngine: 准备打开视频: $uri');
    logger.i(
      'MediaKitEngine: 当前解码模式: ${_usingSoftwareDecoding ? "软解码" : "硬解码"}',
    );

    try {
      // 监听播放错误（在打开之前设置）
      _player!.stream.error.listen((error) {
        logger.e('MediaKitEngine: 播放错误: $error');
        _handlePlaybackError(uri, error);
      });

      // 尝试打开视频
      logger.i('MediaKitEngine: 开始打开媒体文件');
      await _player!.open(mk.Media(uri));

      logger.i('MediaKitEngine: 成功打开视频: $uri');

      // 如果之前失败过，记录成功信息
      if (_failedWithHardware) {
        logger.i('MediaKitEngine: 使用软解码成功播放之前失败的视频');
      }
    } catch (e) {
      logger.e('MediaKitEngine: 打开视频失败: $e');
      logger.e('MediaKitEngine: 错误类型: ${e.runtimeType}');

      // 检查是否是编解码器相关错误
      final errorString = e.toString().toLowerCase();
      final isCodecError =
          errorString.contains('codec') ||
          errorString.contains('decoder') ||
          errorString.contains('format') ||
          errorString.contains('unsupported');

      logger.i('MediaKitEngine: 是否为编解码器错误: $isCodecError');

      // 如果使用硬解失败，尝试切换到软解
      if (!_usingSoftwareDecoding && isCodecError) {
        logger.i('MediaKitEngine: 检测到编解码器错误，尝试切换到软解码');
        await _handlePlaybackError(uri, e.toString());
      } else {
        // 如果已经是软解还失败，或者不是编解码器错误，则抛出异常
        logger.e('MediaKitEngine: 无法通过切换解码方式解决，抛出异常');
        rethrow;
      }
    }
  }

  /// 处理播放错误，尝试切换到软解码
  Future<void> _handlePlaybackError(String uri, dynamic error) async {
    // 如果已经在使用软解码或已经尝试过切换，则不再尝试
    if (_usingSoftwareDecoding || _failedWithHardware) {
      logger.w('MediaKitEngine: 已经在使用软解码或已尝试过切换，跳过错误处理');
      return;
    }

    logger.i('MediaKitEngine: 硬解播放失败，尝试切换到软解码');
    logger.i('MediaKitEngine: 错误详情: $error');

    _failedWithHardware = true;
    _usingSoftwareDecoding = true;

    try {
      // 停止当前播放
      logger.i('MediaKitEngine: 停止当前播放器');
      await _player?.stop();

      // 重新创建播放器配置，强制使用软解码
      logger.i('MediaKitEngine: 创建软解码播放器配置');
      final playerConfig = _createPlayerConfiguration(
        HwDecPolicy.forceSoftware,
      );

      // 重新初始化播放器
      logger.i('MediaKitEngine: 重新初始化播放器');
      await _player?.dispose();
      _player = mk.Player(configuration: playerConfig);

      // 设置错误监听器
      _player!.stream.error.listen((error) {
        logger.e('MediaKitEngine: 软解播放错误: $error');
      });

      await _player!.setPlaylistMode(mk.PlaylistMode.loop);
      _videoController = mkv.VideoController(_player!);

      // 重新打开视频
      logger.i('MediaKitEngine: 使用软解码重新打开视频');
      await _player!.open(mk.Media(uri));
      logger.i('MediaKitEngine: 切换到软解码后成功打开视频');
    } catch (e) {
      logger.e('MediaKitEngine: 切换到软解码后仍然失败: $e');
      // 如果软解也失败，则抛出异常
      throw Exception('视频播放失败，硬解和软解都无法播放: $e');
    }
  }

  @override
  Future<void> pause() async {
    await _player?.pause();
  }

  @override
  Future<void> play() async {
    await _player?.play();
  }

  @override
  Future<void> stop() async {
    await _player?.stop();
  }

  @override
  Future<void> dispose() async {
    try {
      await _player?.dispose();
    } catch (e) {
      logger.w('MediaKitEngine: player dispose error: $e');
    }
    _videoController = null; // Video widget controller will be GC'd
  }

  @override
  Widget buildView() {
    if (_videoController == null) {
      return const SizedBox();
    }

    // 如果是软解码，添加一些优化设置
    if (_usingSoftwareDecoding) {
      return mkv.Video(
        controller: _videoController!,
        // 软解码时使用cover模式，铺满屏幕且内容居中
        fit: BoxFit.cover,
        filterQuality: FilterQuality.low,
      );
    }

    // 使用cover模式，自动铺满屏幕且内容居中
    return mkv.Video(controller: _videoController!, fit: BoxFit.cover);
  }
}

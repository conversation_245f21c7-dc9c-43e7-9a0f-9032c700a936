import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../utils/h265_compatibility_detector.dart';
import 'logger_service.dart';

/// 视频性能监控和优化服务
class VideoPerformanceService {
  static final VideoPerformanceService _instance =
      VideoPerformanceService._internal();
  factory VideoPerformanceService() => _instance;
  VideoPerformanceService._internal();

  static VideoPerformanceService get instance => _instance;

  Timer? _performanceMonitorTimer;
  final Map<String, dynamic> _performanceMetrics = {};
  bool _isMonitoring = false;

  /// 初始化性能监控
  Future<void> initialize() async {
    logger.i('VideoPerformanceService: 初始化性能监控');
    await _collectSystemInfo();
    _startPerformanceMonitoring();
  }

  /// 收集系统信息
  Future<void> _collectSystemInfo() async {
    try {
      // 获取设备兼容性信息
      final compatibilityInfo =
          await H265CompatibilityDetector.getCompatibilityInfo();

      _performanceMetrics['device'] = {
        'model': compatibilityInfo.deviceModel,
        'manufacturer': compatibilityInfo.manufacturer,
        'chipset': compatibilityInfo.chipset,
        'isRK3568': compatibilityInfo.isRK3568,
        'hasHardwareDecoder': compatibilityInfo.hasHardwareDecoder,
        'softwarePerformance': compatibilityInfo.softwareDecoderPerformance
            .toString(),
      };

      // 获取内存信息
      if (Platform.isAndroid) {
        try {
          final memoryInfo = await _getAndroidMemoryInfo();
          _performanceMetrics['memory'] = memoryInfo;
        } catch (e) {
          logger.w('VideoPerformanceService: 获取内存信息失败: $e');
        }
      }

      logger.i('VideoPerformanceService: 系统信息收集完成: $_performanceMetrics');
    } catch (e) {
      logger.e('VideoPerformanceService: 收集系统信息失败: $e');
    }
  }

  /// 获取Android内存信息
  Future<Map<String, dynamic>> _getAndroidMemoryInfo() async {
    const platform = MethodChannel('video_performance');
    try {
      final result = await platform.invokeMethod('getMemoryInfo');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      logger.w('VideoPerformanceService: 获取Android内存信息失败: $e');
      return {};
    }
  }

  /// 开始性能监控
  void _startPerformanceMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _performanceMonitorTimer = Timer.periodic(
      const Duration(seconds: 5),
      (timer) => _collectPerformanceMetrics(),
    );

    logger.i('VideoPerformanceService: 性能监控已启动');
  }

  /// 收集性能指标
  void _collectPerformanceMetrics() {
    try {
      final now = DateTime.now();

      // 记录当前时间戳
      _performanceMetrics['lastUpdate'] = now.millisecondsSinceEpoch;

      // 这里可以添加更多性能指标收集
      // 例如：CPU使用率、内存使用率、帧率等
    } catch (e) {
      logger.w('VideoPerformanceService: 收集性能指标失败: $e');
    }
  }

  /// 获取推荐的视频配置
  Map<String, dynamic> getRecommendedVideoConfig() {
    final device = _performanceMetrics['device'] as Map<String, dynamic>?;
    final memory = _performanceMetrics['memory'] as Map<String, dynamic>?;

    // 默认配置
    var config = {
      'useHardwareDecoding': true,
      'bufferSize': 32 * 1024 * 1024, // 32MB
      'maxBufferSize': 64 * 1024 * 1024, // 64MB
      'filterQuality': 'medium',
      'enableOptimizations': true,
      'isUltraLowPerformance': false,
    };

    if (device != null) {
      // 检查是否为极低性能设备
      final isRK3128 =
          device['chipset']?.toString().toLowerCase().contains('rk3128') ==
          true;
      final isLowPerformanceRockchip = _isLowPerformanceRockchip(
        device['chipset']?.toString() ?? '',
      );
      final isLowPerformance =
          device['softwarePerformance'] == 'SoftwareDecoderPerformance.low';
      final isRK3568 = device['isRK3568'] == true;

      // RK3128和其他极低性能设备的特殊配置
      if (isRK3128 || isLowPerformanceRockchip) {
        logger.w('VideoPerformanceService: 检测到极低性能设备，使用最激进优化');
        config = {
          'useHardwareDecoding': false,
          'bufferSize': 4 * 1024 * 1024, // 4MB极小缓存
          'maxBufferSize': 8 * 1024 * 1024, // 8MB最大缓存
          'filterQuality': 'none', // 无过滤
          'enableOptimizations': true,
          'isUltraLowPerformance': true,
          'forceMinimalSettings': true,
        };
      } else if (isLowPerformance || isRK3568) {
        config = {
          'useHardwareDecoding': false,
          'bufferSize': 16 * 1024 * 1024, // 16MB
          'maxBufferSize': 32 * 1024 * 1024, // 32MB
          'filterQuality': 'low',
          'enableOptimizations': true,
          'isUltraLowPerformance': false,
        };
      }

      // 根据内存情况进一步调整
      if (memory != null) {
        final availableMemory = memory['availableMemory'] as int? ?? 0;
        if (availableMemory > 0 && availableMemory < 512 * 1024 * 1024) {
          // 小于512MB
          config['bufferSize'] = (config['bufferSize'] as int) ~/ 2;
          config['maxBufferSize'] = (config['maxBufferSize'] as int) ~/ 2;
        }
      }
    }

    logger.i('VideoPerformanceService: 推荐配置: $config');
    return config;
  }

  /// 检查是否为低性能Rockchip芯片
  bool _isLowPerformanceRockchip(String chipset) {
    final chipsetLower = chipset.toLowerCase();
    return chipsetLower.contains('rk3128') ||
        chipsetLower.contains('rk3126') ||
        chipsetLower.contains('rk3036') ||
        chipsetLower.contains('rk3229') ||
        chipsetLower.contains('rk3228') ||
        chipsetLower.contains('rk3188');
  }

  /// 获取性能优化建议
  List<String> getPerformanceOptimizations() {
    final device = _performanceMetrics['device'] as Map<String, dynamic>?;
    final optimizations = <String>[];

    if (device != null) {
      final isRK3128 =
          device['chipset']?.toString().toLowerCase().contains('rk3128') ==
          true;
      final isLowPerformanceRockchip = _isLowPerformanceRockchip(
        device['chipset']?.toString() ?? '',
      );
      final isLowPerformance =
          device['softwarePerformance'] == 'SoftwareDecoderPerformance.low';
      final isRK3568 = device['isRK3568'] == true;
      final hasHardwareDecoder = device['hasHardwareDecoder'] == true;

      if (isRK3128 || isLowPerformanceRockchip) {
        optimizations.addAll([
          '🚨 极低性能设备专用优化',
          '强制使用软件解码（硬解会导致严重卡顿）',
          '使用最小缓存（4MB）减少内存压力',
          '禁用所有视频过滤效果',
          '降低视频分辨率到最低',
          '关闭音频处理减少CPU负载',
          '使用最简单的渲染模式',
          '避免播放高分辨率视频',
          '建议播放720p以下视频',
        ]);
      } else if (isRK3568) {
        optimizations.addAll([
          '使用软件解码避免RK3568硬件解码问题',
          '降低视频渲染质量减少GPU负载',
          '减少缓存大小节省内存',
        ]);
      } else if (isLowPerformance) {
        optimizations.addAll(['使用较小的缓存大小', '降低视频过滤质量', '禁用不必要的视频效果']);
      } else if (hasHardwareDecoder) {
        optimizations.addAll(['优先使用硬件解码提高性能', '增加缓存大小改善播放流畅度', '启用高质量视频渲染']);
      }

      optimizations.addAll([
        '使用RepaintBoundary减少重绘',
        '静音播放减少音频处理开销',
        '禁用视频控制条减少UI开销',
      ]);
    }

    return optimizations;
  }

  /// 报告视频播放问题
  void reportPlaybackIssue(
    String videoPath,
    String error,
    Map<String, dynamic> context,
  ) {
    logger.w('VideoPerformanceService: 播放问题报告');
    logger.w('视频路径: $videoPath');
    logger.w('错误信息: $error');
    logger.w('上下文: $context');
    logger.w('设备信息: ${_performanceMetrics['device']}');

    // 这里可以添加问题统计和分析逻辑
  }

  /// 获取当前性能指标
  Map<String, dynamic> getCurrentMetrics() {
    return Map<String, dynamic>.from(_performanceMetrics);
  }

  /// 停止性能监控
  void dispose() {
    _performanceMonitorTimer?.cancel();
    _performanceMonitorTimer = null;
    _isMonitoring = false;
    logger.i('VideoPerformanceService: 性能监控已停止');
  }
}

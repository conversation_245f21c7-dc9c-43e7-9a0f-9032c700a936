import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// H.265兼容性检测工具
/// 专门检测设备对H.265视频编解码的支持情况
class H265CompatibilityDetector {
  static const String _channel = 'h265_compatibility';
  static const MethodChannel _methodChannel = MethodChannel(_channel);

  // 缓存检测结果
  static H265CompatibilityInfo? _cachedInfo;

  /// 获取设备H.265兼容性信息
  static Future<H265CompatibilityInfo> getCompatibilityInfo() async {
    if (_cachedInfo != null) {
      return _cachedInfo!;
    }

    try {
      final info = H265CompatibilityInfo();

      // 检测设备型号和系统信息
      await _detectDeviceInfo(info);

      // 检测硬件解码支持
      await _detectHardwareDecodingSupport(info);

      // 检测软件解码支持
      await _detectSoftwareDecodingSupport(info);

      // 检测WebView兼容性
      await _detectWebViewCompatibility(info);

      // 根据检测结果推荐策略
      _recommendStrategy(info);

      _cachedInfo = info;
      return info;
    } catch (e) {
      debugPrint(
        'H265CompatibilityDetector: Error detecting compatibility: $e',
      );
      return H265CompatibilityInfo.createFallback();
    }
  }

  /// 检测设备信息
  static Future<void> _detectDeviceInfo(H265CompatibilityInfo info) async {
    try {
      if (Platform.isAndroid) {
        // 获取设备型号、制造商、Android版本等信息
        final deviceInfo = await _methodChannel.invokeMethod('getDeviceInfo');
        if (deviceInfo != null) {
          info.deviceModel = deviceInfo['model'] ?? 'Unknown';
          info.manufacturer = deviceInfo['manufacturer'] ?? 'Unknown';
          info.androidVersion = deviceInfo['androidVersion'] ?? 'Unknown';
          info.apiLevel = deviceInfo['apiLevel'] ?? 0;
          info.chipset = deviceInfo['chipset'] ?? 'Unknown';
        }
      }

      // 特殊设备检测
      info.isRK3568 =
          info.chipset.toLowerCase().contains('rk3568') ||
          info.deviceModel.toLowerCase().contains('rk3568');

      // RK3128检测（低性能设备）
      info.isRK3128 =
          info.chipset.toLowerCase().contains('rk3128') ||
          info.deviceModel.toLowerCase().contains('rk3128');

      // 低性能Rockchip芯片检测
      final chipsetLower = info.chipset.toLowerCase();
      info.isLowPerformanceRockchip =
          chipsetLower.contains('rk3128') ||
          chipsetLower.contains('rk3126') ||
          chipsetLower.contains('rk3036') ||
          chipsetLower.contains('rk3229') ||
          chipsetLower.contains('rk3228') ||
          chipsetLower.contains('rk3188');

      debugPrint(
        'H265CompatibilityDetector: Device - ${info.manufacturer} ${info.deviceModel}, Chipset: ${info.chipset}',
      );
    } catch (e) {
      debugPrint('H265CompatibilityDetector: Error detecting device info: $e');
    }
  }

  /// 检测硬件解码支持
  static Future<void> _detectHardwareDecodingSupport(
    H265CompatibilityInfo info,
  ) async {
    try {
      if (Platform.isAndroid) {
        final result = await _methodChannel.invokeMethod(
          'checkH265HardwareSupport',
        );
        if (result != null) {
          info.hasHardwareDecoder = result['hasDecoder'] ?? false;
          info.hardwareDecoderName = result['decoderName'] ?? '';
          info.supportedProfiles = List<String>.from(result['profiles'] ?? []);
          info.maxResolution = result['maxResolution'] ?? '';
          info.hardwareDecoderReliable = result['reliable'] ?? false;
        }
      }

      debugPrint(
        'H265CompatibilityDetector: Hardware decoder - ${info.hasHardwareDecoder ? "Available" : "Not available"}',
      );
      if (info.hasHardwareDecoder) {
        debugPrint(
          'H265CompatibilityDetector: Decoder name: ${info.hardwareDecoderName}',
        );
        debugPrint(
          'H265CompatibilityDetector: Reliable: ${info.hardwareDecoderReliable}',
        );
      }
    } catch (e) {
      debugPrint(
        'H265CompatibilityDetector: Error checking hardware support: $e',
      );
      info.hasHardwareDecoder = false;
      info.hardwareDecoderReliable = false;
    }
  }

  /// 检测软件解码支持
  static Future<void> _detectSoftwareDecodingSupport(
    H265CompatibilityInfo info,
  ) async {
    try {
      // 检测FFmpeg/软件解码库支持
      info.hasSoftwareDecoder = true; // VLC通常都支持软件解码
      info.softwareDecoderPerformance = _estimateSoftwarePerformance(info);

      debugPrint(
        'H265CompatibilityDetector: Software decoder performance: ${info.softwareDecoderPerformance}',
      );
    } catch (e) {
      debugPrint(
        'H265CompatibilityDetector: Error checking software support: $e',
      );
      info.hasSoftwareDecoder = true; // 默认假设支持
      info.softwareDecoderPerformance = SoftwareDecoderPerformance.medium;
    }
  }

  /// 检测WebView兼容性
  static Future<void> _detectWebViewCompatibility(
    H265CompatibilityInfo info,
  ) async {
    try {
      if (Platform.isAndroid) {
        final result = await _methodChannel.invokeMethod(
          'checkWebViewH265Support',
        );
        if (result != null) {
          info.webViewSupportsH265 = result['supportsH265'] ?? false;
          info.webViewVersion = result['webViewVersion'] ?? '';
          info.chromiumVersion = result['chromiumVersion'] ?? '';
        }
      }

      debugPrint(
        'H265CompatibilityDetector: WebView H.265 support: ${info.webViewSupportsH265}',
      );
    } catch (e) {
      debugPrint(
        'H265CompatibilityDetector: Error checking WebView support: $e',
      );
      info.webViewSupportsH265 = false;
    }
  }

  /// 估算软件解码性能
  static SoftwareDecoderPerformance _estimateSoftwarePerformance(
    H265CompatibilityInfo info,
  ) {
    // 根据设备信息估算软件解码性能

    // RK3128和其他低性能Rockchip芯片性能极低
    if (info.isRK3128 || info.isLowPerformanceRockchip) {
      debugPrint('H265CompatibilityDetector: 检测到低性能Rockchip芯片，设置为最低性能');
      return SoftwareDecoderPerformance.low;
    }

    // RK3568性能相对较低
    if (info.isRK3568) {
      return SoftwareDecoderPerformance.low;
    }

    if (info.apiLevel >= 28) {
      return SoftwareDecoderPerformance.high;
    } else if (info.apiLevel >= 24) {
      return SoftwareDecoderPerformance.medium;
    } else {
      return SoftwareDecoderPerformance.low;
    }
  }

  /// 推荐解码策略
  static void _recommendStrategy(H265CompatibilityInfo info) {
    // RK3128和其他低性能Rockchip芯片需要最保守的策略
    if (info.isRK3128 || info.isLowPerformanceRockchip) {
      debugPrint('H265CompatibilityDetector: 低性能Rockchip设备，使用最保守策略');
      info.recommendedStrategy = H265DecodingStrategy.forceSoftware;
      info.recommendedWebViewMode = WebViewRenderingMode.softwareOnly;
      return;
    }

    if (info.isRK3568 || !info.hardwareDecoderReliable) {
      // RK3568或硬件解码不可靠时，强制使用软件解码
      info.recommendedStrategy = H265DecodingStrategy.forceSoftware;
      info.recommendedWebViewMode = WebViewRenderingMode.softwareOnly;
    } else if (info.hasHardwareDecoder && info.hardwareDecoderReliable) {
      // 硬件解码可用且可靠
      info.recommendedStrategy = H265DecodingStrategy.hardwareFirst;
      info.recommendedWebViewMode = WebViewRenderingMode.hybrid;
    } else {
      // 默认策略
      info.recommendedStrategy = H265DecodingStrategy.softwareFirst;
      info.recommendedWebViewMode = WebViewRenderingMode.softwareOnly;
    }

    debugPrint(
      'H265CompatibilityDetector: Recommended strategy: ${info.recommendedStrategy}',
    );
    debugPrint(
      'H265CompatibilityDetector: Recommended WebView mode: ${info.recommendedWebViewMode}',
    );
  }

  /// 清除缓存，强制重新检测
  static void clearCache() {
    _cachedInfo = null;
  }

  /// 检查是否为已知的问题设备
  static bool isKnownProblematicDevice(H265CompatibilityInfo info) {
    final problematicDevices = [
      'rk3128', // 低性能设备，严重卡顿
      'rk3126',
      'rk3036',
      'rk3229',
      'rk3228',
      'rk3188',
      'rk3568',
      'rk3566',
      'allwinner h6',
      'amlogic s905',
    ];

    final deviceIdentifier =
        '${info.manufacturer} ${info.deviceModel} ${info.chipset}'
            .toLowerCase();

    return problematicDevices.any(
      (device) => deviceIdentifier.contains(device),
    );
  }
}

/// H.265兼容性信息
class H265CompatibilityInfo {
  // 设备信息
  String deviceModel = 'Unknown';
  String manufacturer = 'Unknown';
  String androidVersion = 'Unknown';
  int apiLevel = 0;
  String chipset = 'Unknown';
  bool isRK3568 = false;
  bool isRK3128 = false;
  bool isLowPerformanceRockchip = false;

  // 硬件解码支持
  bool hasHardwareDecoder = false;
  String hardwareDecoderName = '';
  List<String> supportedProfiles = [];
  String maxResolution = '';
  bool hardwareDecoderReliable = false;

  // 软件解码支持
  bool hasSoftwareDecoder = false;
  SoftwareDecoderPerformance softwareDecoderPerformance =
      SoftwareDecoderPerformance.medium;

  // WebView兼容性
  bool webViewSupportsH265 = false;
  String webViewVersion = '';
  String chromiumVersion = '';

  // 推荐策略
  H265DecodingStrategy recommendedStrategy = H265DecodingStrategy.softwareFirst;
  WebViewRenderingMode recommendedWebViewMode =
      WebViewRenderingMode.softwareOnly;

  /// 创建回退配置（当检测失败时使用）
  static H265CompatibilityInfo createFallback() {
    final info = H265CompatibilityInfo();
    info.hasHardwareDecoder = false;
    info.hasSoftwareDecoder = true;
    info.webViewSupportsH265 = false;
    info.recommendedStrategy = H265DecodingStrategy.forceSoftware;
    info.recommendedWebViewMode = WebViewRenderingMode.softwareOnly;
    info.softwareDecoderPerformance = SoftwareDecoderPerformance.low;
    return info;
  }

  @override
  String toString() {
    return 'H265CompatibilityInfo{\n'
        '  device: $manufacturer $deviceModel ($chipset)\n'
        '  android: $androidVersion (API $apiLevel)\n'
        '  hardwareDecoder: $hasHardwareDecoder (reliable: $hardwareDecoderReliable)\n'
        '  softwareDecoder: $hasSoftwareDecoder (performance: $softwareDecoderPerformance)\n'
        '  webViewSupport: $webViewSupportsH265\n'
        '  recommendedStrategy: $recommendedStrategy\n'
        '  recommendedWebViewMode: $recommendedWebViewMode\n'
        '}';
  }
}

/// H.265解码策略
enum H265DecodingStrategy {
  /// 硬件解码优先，失败时回退到软件解码
  hardwareFirst,

  /// 软件解码优先
  softwareFirst,

  /// 强制使用软件解码
  forceSoftware,

  /// 禁用H.265播放（转码或拒绝播放）
  disabled,
}

/// 软件解码性能等级
enum SoftwareDecoderPerformance {
  high, // 高性能，可以流畅播放1080p
  medium, // 中等性能，可以播放720p
  low, // 低性能，建议480p或转码
}

/// WebView渲染模式
enum WebViewRenderingMode {
  /// 混合模式（硬件+软件）
  hybrid,

  /// 仅软件渲染
  softwareOnly,

  /// 虚拟显示模式
  virtualDisplay,
}

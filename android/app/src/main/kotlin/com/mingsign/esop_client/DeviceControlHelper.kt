package com.mingsign.esop_client

import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioManager
import android.os.Build
import android.util.Log
import java.io.BufferedReader
import java.io.InputStreamReader

class DeviceControlHelper(private val context: Context) {

    companion object {
        private const val TAG = "DeviceControlHelper"
    }

    fun rebootWithRoot(): Boolean {
        Log.d(TAG, "尝试使用root权限重启...")
        return executeRootCommand("reboot") || executeRootCommand("sync && reboot")
    }

    fun shutdownWithRoot(): Boolean {
        Log.d(TAG, "尝试使用root权限关机...")
        // 针对RK3568设备优化关机命令
        return executeRootCommand("shutdown -p") || 
               executeRootCommand("poweroff") || 
               executeRootCommand("halt -p") ||
               executeRootCommand("reboot -p")
    }

    fun rebootWithSystem(): Boolean {
        Log.d(TAG, "尝试使用系统级方法重启...")
        return try {
            val powerManager =
                    context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            powerManager.reboot(null)
            true
        } catch (e: Exception) {
            Log.e(TAG, "系统级重启失败", e)
            executeShellCommand("reboot")
        }
    }

    fun shutdownWithSystem(): Boolean {
        Log.d(TAG, "尝试使用系统级方法关机...")
        return try {
            val powerManager =
                    context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            
            // 针对RK3568等设备的关机修复
            // 使用反射调用shutdown方法而不是reboot("shutdown")
            try {
                val shutdownMethod = android.os.PowerManager::class.java.getMethod(
                    "shutdown", Boolean::class.javaPrimitiveType, String::class.java, Boolean::class.javaPrimitiveType
                )
                shutdownMethod.invoke(powerManager, false, "Device shutdown via MQTT", false)
                Log.d(TAG, "使用PowerManager.shutdown()关机成功")
                true
            } catch (e: Exception) {
                Log.w(TAG, "PowerManager.shutdown()方法失败，尝试shell命令", e)
                // 备选方法：直接使用shell命令关机
                executeShellCommand("shutdown -p")
            }
        } catch (e: Exception) {
            Log.e(TAG, "系统级关机失败", e)
            executeShellCommand("shutdown -p")
        }
    }

    /** 检查音量修改权限 */
    private fun checkVolumePermissions(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val hasModifyAudioSettings =
                        context.checkSelfPermission(
                                android.Manifest.permission.MODIFY_AUDIO_SETTINGS
                        ) == PackageManager.PERMISSION_GRANTED

                Log.d(TAG, "MODIFY_AUDIO_SETTINGS权限: $hasModifyAudioSettings")
                hasModifyAudioSettings
            } else {
                true // Android 6.0以下默认有权限
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查音量权限失败", e)
            false
        }
    }

    /** 设置系统音量 */
    fun setSystemVolume(volume: Int): Boolean {
        return try {
            Log.d(TAG, "开始设置音量: $volume%")

            // 验证音量范围
            if (volume < 0 || volume > 100) {
                Log.e(TAG, "音量值超出范围: $volume")
                return false
            }

            // 检查权限
            if (!checkVolumePermissions()) {
                Log.e(TAG, "缺少音量修改权限，直接尝试root方法")
                return setVolumeWithRoot(volume)
            }

            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // 尝试多次设置，增加成功率
            var success = false
            val maxRetries = 3

            for (attempt in 1..maxRetries) {
                Log.d(TAG, "音量设置尝试 $attempt/$maxRetries")

                // 设置多个音频流以确保兼容性
                val streamTypes =
                        arrayOf(
                                AudioManager.STREAM_SYSTEM,
                                AudioManager.STREAM_MUSIC,
                                AudioManager.STREAM_NOTIFICATION,
                                AudioManager.STREAM_ALARM
                        )

                for (streamType in streamTypes) {
                    try {
                        val maxVolume = audioManager.getStreamMaxVolume(streamType)
                        val targetVolume = (volume * maxVolume / 100).coerceIn(0, maxVolume)

                        // 使用AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE来确保设置生效
                        audioManager.setStreamVolume(
                                streamType,
                                targetVolume,
                                AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE
                        )
                        Log.d(TAG, "设置音频流 $streamType 音量成功: $targetVolume/$maxVolume")
                    } catch (e: Exception) {
                        Log.w(TAG, "设置音频流 $streamType 失败", e)
                    }
                }

                // 等待设置生效
                Thread.sleep(300)

                // 验证设置是否成功
                if (verifyVolumeSet(volume)) {
                    success = true
                    Log.d(TAG, "音量设置成功，尝试次数: $attempt")
                    break
                } else {
                    Log.w(TAG, "第 $attempt 次尝试验证失败")
                }
            }

            if (!success) {
                Log.e(TAG, "常规方法设置音量失败，尝试系统级方法")
                success = setVolumeWithSystemApi(volume)
            }

            if (!success) {
                Log.e(TAG, "系统级方法失败，尝试root权限")
                success = setVolumeWithRoot(volume)
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "设置系统音量异常", e)
            // 尝试使用root权限设置音量
            setVolumeWithRoot(volume)
        }
    }

    /** 使用系统级API设置音量（通过反射） */
    private fun setVolumeWithSystemApi(volume: Int): Boolean {
        return try {
            Log.d(TAG, "尝试使用系统级API设置音量: $volume%")

            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // 尝试使用隐藏的setMasterVolume方法
            try {
                val setMasterVolumeMethod =
                        AudioManager::class.java.getDeclaredMethod(
                                "setMasterVolume",
                                Int::class.java,
                                Int::class.java
                        )
                setMasterVolumeMethod.isAccessible = true
                setMasterVolumeMethod.invoke(audioManager, volume, 0)
                Log.d(TAG, "setMasterVolume调用成功")

                Thread.sleep(500)
                if (verifyVolumeSet(volume)) {
                    return true
                }
            } catch (e: Exception) {
                Log.w(TAG, "setMasterVolume方法失败", e)
            }

            // 尝试使用AudioService的setStreamVolume方法
            try {
                val audioServiceClass = Class.forName("android.media.AudioService")
                val setStreamVolumeMethod =
                        audioServiceClass.getDeclaredMethod(
                                "setStreamVolume",
                                Int::class.java,
                                Int::class.java,
                                Int::class.java,
                                String::class.java
                        )
                setStreamVolumeMethod.isAccessible = true

                val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_SYSTEM)
                val targetVolume = (volume * maxVolume / 100).coerceIn(0, maxVolume)

                setStreamVolumeMethod.invoke(
                        null,
                        AudioManager.STREAM_SYSTEM,
                        targetVolume,
                        0,
                        context.packageName
                )
                Log.d(TAG, "AudioService.setStreamVolume调用成功")

                Thread.sleep(500)
                if (verifyVolumeSet(volume)) {
                    return true
                }
            } catch (e: Exception) {
                Log.w(TAG, "AudioService.setStreamVolume方法失败", e)
            }

            false
        } catch (e: Exception) {
            Log.e(TAG, "系统级API设置音量失败", e)
            false
        }
    }

    /** 使用root权限设置音量 */
    private fun setVolumeWithRoot(volume: Int): Boolean {
        return try {
            Log.d(TAG, "尝试使用root权限设置音量: $volume%")

            // 尝试多种root命令设置音量
            val commands =
                    arrayOf(
                            "media volume --stream 3 --set $volume",
                            "media volume --stream 1 --set $volume",
                            "media volume --stream 5 --set $volume",
                            "service call audio 7 i32 3 i32 $volume i32 0", // 直接调用AudioService
                            "am broadcast -a android.media.VOLUME_CHANGED_ACTION --ei android.media.EXTRA_VOLUME_STREAM_TYPE 3 --ei android.media.EXTRA_VOLUME_STREAM_VALUE $volume"
                    )

            for (command in commands) {
                Log.d(TAG, "尝试root命令: $command")
                if (executeRootCommand(command)) {
                    // 验证设置是否成功
                    Thread.sleep(500) // 等待设置生效
                    if (verifyVolumeSet(volume)) {
                        Log.d(TAG, "Root音量设置成功: $volume%")
                        return true
                    }
                }
            }

            Log.w(TAG, "所有root音量设置方法都失败")
            false
        } catch (e: Exception) {
            Log.e(TAG, "使用root权限设置音量失败", e)
            false
        }
    }

    /** 验证音量设置是否成功 */
    private fun verifyVolumeSet(expectedVolume: Int): Boolean {
        return try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val currentSystemVolume = audioManager.getStreamVolume(AudioManager.STREAM_SYSTEM)
            val maxSystemVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_SYSTEM)
            val currentPercentage =
                    if (maxSystemVolume > 0) {
                        (currentSystemVolume * 100 / maxSystemVolume)
                    } else {
                        0
                    }

            // 允许±10%的误差范围
            val tolerance = 10
            val success = Math.abs(currentPercentage - expectedVolume) <= tolerance

            Log.d(TAG, "音量验证: 期望=$expectedVolume%, 实际=$currentPercentage%, 成功=$success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "音量验证失败", e)
            false
        }
    }

    /** 使用root权限执行命令 */
    private fun executeRootCommand(command: String): Boolean {
        return try {
            Log.d(TAG, "执行root命令: $command")

            // 修复su命令格式，使用正确的参数传递方式
            val process =
                    ProcessBuilder().command("su", "-c", command).redirectErrorStream(false).start()

            val exitCode = process.waitFor()

            // 读取错误输出
            val errorReader = BufferedReader(InputStreamReader(process.errorStream))
            val errorOutput = errorReader.readText()
            if (errorOutput.isNotEmpty()) {
                Log.w(TAG, "命令执行错误输出: $errorOutput")
            }

            // 读取标准输出
            val outputReader = BufferedReader(InputStreamReader(process.inputStream))
            val output = outputReader.readText()
            if (output.isNotEmpty()) {
                Log.d(TAG, "命令执行输出: $output")
            }

            val success = exitCode == 0
            Log.d(TAG, "Root命令执行结果: exitCode=$exitCode, success=$success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行root命令失败: $command", e)
            // 尝试备用方法
            executeRootCommandAlternative(command)
        }
    }

    /** 备用root命令执行方法 */
    private fun executeRootCommandAlternative(command: String): Boolean {
        return try {
            Log.d(TAG, "尝试备用root命令执行方法: $command")

            // 方法1：直接使用Runtime.exec
            val process1 = Runtime.getRuntime().exec("su")
            val outputStream = process1.outputStream
            outputStream.write("$command\n".toByteArray())
            outputStream.write("exit\n".toByteArray())
            outputStream.flush()
            outputStream.close()

            val exitCode1 = process1.waitFor()
            if (exitCode1 == 0) {
                Log.d(TAG, "备用方法1成功")
                return true
            }

            // 方法2：使用sh -c包装
            val process2 = Runtime.getRuntime().exec(arrayOf("su", "-c", "sh -c '$command'"))
            val exitCode2 = process2.waitFor()

            val success = exitCode2 == 0
            Log.d(TAG, "备用方法2结果: exitCode=$exitCode2, success=$success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "备用root命令执行失败: $command", e)
            false
        }
    }

    /** 执行普通shell命令 */
    private fun executeShellCommand(command: String): Boolean {
        return try {
            Log.d(TAG, "执行shell命令: $command")
            val process = Runtime.getRuntime().exec(command)
            val exitCode = process.waitFor()

            val success = exitCode == 0
            Log.d(TAG, "Shell命令执行结果: exitCode=$exitCode, success=$success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行shell命令失败: $command", e)
            false
        }
    }

    /** 使用ysapi.jar执行重启 - 保底方法 通过反射调用ysapi.jar中的重启方法 */
    fun executeYsApiReboot(): Boolean {
        return try {
            Log.d(TAG, "尝试使用ysapi.jar执行重启")

            // 通过反射获取MyManager类和方法 - 使用正确的包路径
            val myManagerClass = Class.forName("com.ys.rkapi.MyManager")
            val getInstanceMethod = myManagerClass.getMethod("getInstance", Context::class.java)
            val rebootMethod = myManagerClass.getMethod("reboot")

            // 获取MyManager实例并调用重启方法
            val myManagerInstance = getInstanceMethod.invoke(null, context)
            rebootMethod.invoke(myManagerInstance)

            Log.d(TAG, "ysapi.jar重启方法调用成功")
            true
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "未找到ysapi MyManager类，可能jar文件未正确加载", e)
            false
        } catch (e: NoSuchMethodException) {
            Log.e(TAG, "未找到ysapi重启方法", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "使用ysapi.jar重启失败", e)
            false
        }
    }

    /** 使用ysapi.jar执行关机 - 保底方法 通过反射调用ysapi.jar中的关机方法 */
    fun executeYsApiShutdown(): Boolean {
        return try {
            Log.d(TAG, "尝试使用ysapi.jar执行关机")

            // 通过反射获取MyManager类和方法 - 使用正确的包路径
            val myManagerClass = Class.forName("com.ys.rkapi.MyManager")
            val getInstanceMethod = myManagerClass.getMethod("getInstance", Context::class.java)
            val shutdownMethod = myManagerClass.getMethod("shutdown")

            // 获取MyManager实例并调用关机方法
            val myManagerInstance = getInstanceMethod.invoke(null, context)
            shutdownMethod.invoke(myManagerInstance)

            Log.d(TAG, "ysapi.jar关机方法调用成功")
            true
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "未找到ysapi MyManager类，可能jar文件未正确加载", e)
            false
        } catch (e: NoSuchMethodException) {
            Log.e(TAG, "未找到ysapi关机方法", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "使用ysapi.jar关机失败", e)
            false
        }
    }

    /**
     * 使用ysapi.jar设置每周定时开关机（精准匹配反编译签名） MyManager.setPowerOnOffWithWeekly(int[] on, int[] off, int[]
     * week) 兼容旧方法：MyManager.setPowerOnOff(int[] on, int[] off)
     */
    fun setPowerOnOffWithWeekly(
            powerOnHour: Int,
            powerOnMinute: Int,
            powerOffHour: Int,
            powerOffMinute: Int,
            weekdays: List<Int>
    ): Boolean {
        return try {
            Log.d(
                    TAG,
                    "设置定时开关机(精准调用): on=$powerOnHour:$powerOnMinute off=$powerOffHour:$powerOffMinute weekdays=$weekdays"
            )

            val cls = Class.forName("com.ys.rkapi.MyManager")
            val getInstance = cls.getMethod("getInstance", Context::class.java)
            val mgr = getInstance.invoke(null, context)

            // 绑定AIDL（若存在）
            try {
                val bind = cls.getMethod("bindAIDLService", Context::class.java)
                bind.invoke(mgr, context)
                Log.d(TAG, "bindAIDLService 成功")
                Thread.sleep(300)
            } catch (e: Exception) {
                Log.w(TAG, "bindAIDLService 不存在/失败，继续", e)
            }

            // 可能需要先启用开关机模式
            try {
                val modeM = cls.getMethod("setSystemPowerOnOffMode", Int::class.javaPrimitiveType)
                modeM.invoke(mgr, 1)
                Log.d(TAG, "setSystemPowerOnOffMode(1) 成功")
            } catch (e: Exception) {
                Log.w(TAG, "setSystemPowerOnOffMode 不存在/失败，继续", e)
            }

            val onArr = intArrayOf(powerOnHour, powerOnMinute)
            val offArr = intArrayOf(powerOffHour, powerOffMinute)
            val weekArr =
                    IntArray(7).apply {
                        for (i in 0..6) this[i] = if (i < weekdays.size) weekdays[i] else 0
                    }

            // 主签名：setPowerOnOffWithWeekly(int[], int[], int[])
            try {
                val m =
                        cls.getMethod(
                                "setPowerOnOffWithWeekly",
                                IntArray::class.java,
                                IntArray::class.java,
                                IntArray::class.java
                        )
                val ok = m.invoke(mgr, onArr, offArr, weekArr) as? Boolean ?: true
                Log.d(TAG, "调用 setPowerOnOffWithWeekly 成功: $ok")
                return ok
            } catch (e: NoSuchMethodException) {
                Log.w(TAG, "未找到 setPowerOnOffWithWeekly(int[],int[],int[])", e)
            } catch (e: Exception) {
                Log.e(TAG, "调用 setPowerOnOffWithWeekly 失败", e)
            }

            // 兼容旧方法：setPowerOnOff(int[], int[])
            try {
                val m2 = cls.getMethod("setPowerOnOff", IntArray::class.java, IntArray::class.java)
                val ok2 = m2.invoke(mgr, onArr, offArr) as? Boolean ?: true
                Log.d(TAG, "调用 setPowerOnOff 成功: $ok2（周设置可能不被支持）")
                return ok2
            } catch (e: Exception) {
                Log.e(TAG, "调用 setPowerOnOff 失败", e)
            }

            Log.e(TAG, "未能设置定时开关机（未命中 ysapi 方法）")
            false
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "未找到ysapi MyManager类", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "设置定时开关机异常", e)
            false
        }
    }

    /** 使用ysapi.jar清除定时开关机数据 */
    fun clearPowerOnOffTime(): Boolean {
        return try {
            Log.d(TAG, "尝试使用ysapi.jar清除定时开关机数据")

            val cls = Class.forName("com.ys.rkapi.MyManager")
            val getInstance = cls.getMethod("getInstance", Context::class.java)
            val mgr = getInstance.invoke(null, context)

            // 绑定AIDL服务
            try {
                val bind = cls.getMethod("bindAIDLService", Context::class.java)
                bind.invoke(mgr, context)
                Log.d(TAG, "bindAIDLService 成功")
                Thread.sleep(300)
            } catch (e: Exception) {
                Log.w(TAG, "bindAIDLService 不存在/失败，继续", e)
            }

            // 调用清除定时开关机数据方法
            val clearMethod = cls.getMethod("clearPowerOnOffTime")
            val result = clearMethod.invoke(mgr) as? Boolean ?: true

            Log.d(TAG, "清除定时开关机数据成功: $result")
            result
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "未找到ysapi MyManager类", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "清除定时开关机数据异常", e)
            false
        }
    }

    /** 使用ysapi.jar获取MAC地址 - 备用方法 通过反射调用ysapi.jar中的getEthMacAddress方法 */
    fun getMacAddressWithYsApi(): String {
        return try {
            Log.d(TAG, "尝试使用ysapi.jar获取MAC地址")

            // 通过反射获取MyManager类和方法 - 使用正确的包路径
            val myManagerClass = Class.forName("com.ys.rkapi.MyManager")
            val getInstanceMethod = myManagerClass.getMethod("getInstance", Context::class.java)

            // 获取MyManager实例
            val myManagerInstance = getInstanceMethod.invoke(null, context)
            Log.d(TAG, "MyManager实例创建成功")

            // 根据文档，需要先绑定AIDL服务
            try {
                val bindAIDLServiceMethod =
                        myManagerClass.getMethod("bindAIDLService", Context::class.java)
                bindAIDLServiceMethod.invoke(myManagerInstance, context)
                Log.d(TAG, "AIDL服务绑定成功")

                // 等待服务连接
                Thread.sleep(1000)
            } catch (e: Exception) {
                Log.w(TAG, "绑定AIDL服务失败，尝试直接调用方法", e)
            }

            // 调用getEthMacAddress方法
            val getEthMacAddressMethod = myManagerClass.getMethod("getEthMacAddress")
            val macAddress = getEthMacAddressMethod.invoke(myManagerInstance) as? String

            Log.d(TAG, "ysapi.jar获取MAC地址成功: $macAddress")
            macAddress ?: "unknown_mac_address"
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "未找到ysapi MyManager类，可能jar文件未正确加载", e)
            "unknown_mac_address"
        } catch (e: NoSuchMethodException) {
            Log.e(TAG, "未找到ysapi方法", e)
            "unknown_mac_address"
        } catch (e: Exception) {
            Log.e(TAG, "使用ysapi.jar获取MAC地址失败", e)
            "unknown_mac_address"
        }
    }
}

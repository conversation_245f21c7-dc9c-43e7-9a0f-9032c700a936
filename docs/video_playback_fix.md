# 视频播放黑屏问题修复

## 问题描述

在播放特定视频时出现黑屏，日志显示以下错误：
- `Could not open codec` - 编解码器无法打开
- `Unsupported mime video/mjpeg` - 不支持的MJPEG格式
- `Unsupported mime video/vc1` - 不支持的VC1格式
- 硬件解码失败后自动切换到软解码

## 根本原因

1. **H.265硬件解码检测逻辑问题**：Android原生代码中同时检测H.265和VP9格式，可能导致误判
2. **编解码器兼容性问题**：某些视频格式或编码参数与硬件解码器不兼容
3. **错误处理机制不够智能**：缺乏对特定错误类型的精确识别和处理

## 修复方案

### 1. 优化Android原生H.265检测逻辑

**文件**: `android/app/src/main/kotlin/com/mingsign/esop_client/MainActivity.kt`

**修改内容**:
- 将H.265检测逻辑从同时检测`video/hevc`和`video/x-vnd.on2.vp9`改为只检测`video/hevc`
- 增加详细的调试日志，便于问题排查
- 改进硬件解码器可靠性判断逻辑

```kotlin
// 只检测H.265/HEVC，不包括VP9
if (type.equals("video/hevc", ignoreCase = true)) {
    android.util.Log.d("H265Check", "Found H.265 decoder: ${codec.name}, type: $type")
    // ... 处理逻辑
}
```

### 2. 增强MediaKitEngine错误处理

**文件**: `lib/video/adapters/media_kit_engine.dart`

**修改内容**:
- 增加智能视频格式分析功能
- 改进编解码器错误识别机制
- 优化硬解到软解的切换逻辑
- 增加详细的调试日志

**新增功能**:
```dart
/// 分析视频文件格式和特征
Map<String, dynamic> _analyzeVideoFile(String sourcePath) {
  // 检查H.265、特殊格式、高分辨率等特征
  // 为解码策略选择提供依据
}
```

**改进的错误处理**:
```dart
// 检查是否是编解码器相关错误
final isCodecError = errorString.contains('codec') || 
                    errorString.contains('decoder') ||
                    errorString.contains('format') ||
                    errorString.contains('unsupported');

if (!_usingSoftwareDecoding && isCodecError) {
  // 只有在编解码器错误时才尝试切换到软解码
  await _handlePlaybackError(uri, e.toString());
}
```

### 3. 智能解码策略选择

**新增特性**:
- 根据视频文件名和路径分析视频特征
- 对特殊格式（MJPEG、VC1、VP9）优先使用软解码
- 对高分辨率视频考虑使用软解码
- 对已知问题设备（如RK3568）的H.265视频直接使用软解码

## 修复效果

1. **更准确的硬件解码检测**：避免VP9和H.265混淆导致的误判
2. **智能的解码策略选择**：根据视频特征和设备能力选择最佳解码方式
3. **更好的错误恢复机制**：只在编解码器错误时才尝试切换解码方式
4. **详细的调试信息**：便于问题排查和性能优化

## 测试建议

1. **测试不同格式的视频文件**：
   - H.264/AVC视频
   - H.265/HEVC视频
   - 包含MJPEG、VC1等特殊格式的视频

2. **测试不同分辨率的视频**：
   - 标清视频（720p以下）
   - 高清视频（1080p）
   - 超高清视频（4K）

3. **测试不同设备**：
   - RK3568芯片设备
   - 其他Android设备
   - 不同Android版本

## 日志监控

关键日志标识符：
- `H265Check`: Android原生H.265检测日志
- `MediaKitEngine`: 视频引擎相关日志
- `VideoDecodingStrategyManager`: 解码策略管理日志

## 后续优化建议

1. **添加视频文件元数据分析**：使用FFprobe等工具获取准确的编码信息
2. **实现动态解码策略调整**：根据播放性能实时调整解码策略
3. **添加用户手动解码模式选择**：允许用户在设置中强制选择解码方式
4. **收集播放统计数据**：分析不同视频格式和设备的播放成功率
